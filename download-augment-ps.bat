@echo off
:: 使用PowerShell下载Augment VSCode插件
:: 适用于没有curl的Windows系统

set "VERSION=0.513.0"
if not "%1"=="" set "VERSION=%1"

set "FILENAME=augment-vscode-augment-%VERSION%.vsix"
set "URL=https://marketplace.visualstudio.com/_apis/public/gallery/publishers/augment/vsextensions/vscode-augment/%VERSION%/vspackage"

echo Downloading Augment VSCode Plugin v%VERSION% using PowerShell...
echo URL: %URL%
echo Output: %FILENAME%
echo.

:: 使用PowerShell下载
powershell -Command "try { Invoke-WebRequest -Uri '%URL%' -OutFile '%FILENAME%' -UseBasicParsing; Write-Host 'Download completed: %FILENAME%'; Write-Host 'File size:' (Get-Item '%FILENAME%').Length 'bytes' } catch { Write-Host 'Download failed:' $_.Exception.Message; exit 1 }"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo Download successful!
    echo ========================================
    echo File: %FILENAME%
    echo.
    echo Installation options:
    echo 1. Manual: Open VSCode ^> Ctrl+Shift+P ^> "Extensions: Install from VSIX..." ^> Select %FILENAME%
    echo 2. Command: code --install-extension "%FILENAME%"
    echo.
    
    set /p "INSTALL=Install now? (y/n): "
    if /i "%INSTALL%"=="y" (
        echo Installing...
        code --install-extension "%FILENAME%"
    )
) else (
    echo.
    echo ========================================
    echo Download failed!
    echo ========================================
    echo Version %VERSION% may not exist or network error occurred.
    echo.
    echo Try:
    echo 1. Different version number: %~n0 0.512.0
    echo 2. Check VSCode Extensions panel for available versions
    echo 3. Check internet connection
)

echo.
pause
