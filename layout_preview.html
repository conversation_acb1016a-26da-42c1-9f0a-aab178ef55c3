<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鸿蒙HelloWorld布局预览</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .phone-container {
            width: 375px;
            height: 667px;
            margin: 20px auto;
            background-color: #F5F5F5;
            border: 2px solid #333;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .content-column {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            padding: 40px;
            box-sizing: border-box;
        }
        
        .main-title {
            font-size: 50px;
            font-weight: bold;
            color: #FF1744;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .subtitle {
            font-size: 24px;
            color: #333333;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .counter-text {
            font-size: 18px;
            color: #666666;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .primary-button {
            width: 200px;
            height: 50px;
            font-size: 18px;
            color: white;
            background-color: #007DFF;
            border: none;
            border-radius: 25px;
            margin-bottom: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .primary-button:hover {
            background-color: #0056CC;
            transform: translateY(-2px);
        }
        
        .secondary-button {
            width: 120px;
            height: 40px;
            font-size: 16px;
            color: #007DFF;
            background-color: transparent;
            border: 1px solid #007DFF;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .secondary-button:hover {
            background-color: #007DFF;
            color: white;
        }
        
        .description-text {
            font-size: 14px;
            color: #999999;
            margin-top: 50px;
            text-align: center;
        }
        
        .info-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            font-size: 12px;
            max-width: 200px;
        }
    </style>
</head>
<body>
    <div class="info-panel">
        <h4>布局说明</h4>
        <p>• Row容器(水平布局)</p>
        <p>• Column容器(垂直布局)</p>
        <p>• 居中对齐</p>
        <p>• 响应式间距</p>
        <p>• 交互式按钮</p>
    </div>
    
    <div class="phone-container">
        <div class="content-column">
            <div class="main-title" id="mainTitle">Hello World</div>
            <div class="subtitle">欢迎来到鸿蒙世界！</div>
            <div class="counter-text" id="counterText">点击次数: 0</div>
            <button class="primary-button" onclick="handleClick()">点击我</button>
            <button class="secondary-button" onclick="handleReset()">重置</button>
            <div class="description-text">这是一个鸿蒙HelloWorld应用示例</div>
        </div>
    </div>

    <script>
        let clickCount = 0;
        
        function handleClick() {
            clickCount++;
            document.getElementById('counterText').textContent = `点击次数: ${clickCount}`;
            
            if (clickCount % 5 === 0) {
                document.getElementById('mainTitle').textContent = `Hello World ${clickCount}!`;
            }
        }
        
        function handleReset() {
            clickCount = 0;
            document.getElementById('counterText').textContent = '点击次数: 0';
            document.getElementById('mainTitle').textContent = 'Hello World';
        }
    </script>
</body>
</html>
