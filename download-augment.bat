@echo off
setlocal enabledelayedexpansion

:: 设置变量
set "PLUGIN_VERSION=0.513.0"
set "PUBLISHER=augment"
set "EXTENSION=vscode-augment"
set "FILENAME=%EXTENSION%-%PLUGIN_VERSION%.vsix"
set "DOWNLOAD_URL=https://marketplace.visualstudio.com/_apis/public/gallery/publishers/%PUBLISHER%/vsextensions/%EXTENSION%/%PLUGIN_VERSION%/vspackage"

echo ========================================
echo VSCode Augment Plugin Downloader
echo ========================================
echo.
echo Plugin: %PUBLISHER%.%EXTENSION%
echo Version: %PLUGIN_VERSION%
echo Output: %FILENAME%
echo.

:: 检查是否存在curl命令
where curl >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: curl command not found!
    echo Please install curl or use PowerShell method.
    echo.
    echo Alternative PowerShell command:
    echo Invoke-WebRequest -Uri "%DOWNLOAD_URL%" -OutFile "%FILENAME%"
    pause
    exit /b 1
)

:: 开始下载
echo Downloading %FILENAME%...
echo URL: %DOWNLOAD_URL%
echo.

curl -L "%DOWNLOAD_URL%" -o "%FILENAME%" --progress-bar

:: 检查下载结果
if %errorlevel% equ 0 (
    if exist "%FILENAME%" (
        echo.
        echo ========================================
        echo Download completed successfully!
        echo ========================================
        echo File: %FILENAME%
        
        :: 显示文件大小
        for %%A in ("%FILENAME%") do (
            echo Size: %%~zA bytes
        )
        
        echo.
        echo To install this plugin:
        echo 1. Open VSCode
        echo 2. Press Ctrl+Shift+P
        echo 3. Type: Extensions: Install from VSIX...
        echo 4. Select the file: %FILENAME%
        echo.
        echo Or use command line:
        echo code --install-extension "%FILENAME%"
        echo.
        
        :: 询问是否立即安装
        set /p "INSTALL_NOW=Do you want to install now? (y/n): "
        if /i "!INSTALL_NOW!"=="y" (
            echo.
            echo Installing plugin...
            code --install-extension "%FILENAME%"
            if !errorlevel! equ 0 (
                echo Plugin installed successfully!
            ) else (
                echo Failed to install plugin. Please install manually.
            )
        )
    ) else (
        echo Error: File was not created. Download may have failed.
        echo This might mean version %PLUGIN_VERSION% does not exist.
        echo.
        echo Try these alternatives:
        echo 1. Check available versions in VSCode Extensions panel
        echo 2. Try a different version number
        echo 3. Contact plugin developer
    )
) else (
    echo.
    echo ========================================
    echo Download failed!
    echo ========================================
    echo Error code: %errorlevel%
    echo.
    echo Possible reasons:
    echo 1. Version %PLUGIN_VERSION% does not exist
    echo 2. Network connection issues
    echo 3. Plugin may have been removed
    echo.
    echo Suggestions:
    echo 1. Check your internet connection
    echo 2. Try a different version number
    echo 3. Check VSCode Extensions panel for available versions
)

echo.
pause
