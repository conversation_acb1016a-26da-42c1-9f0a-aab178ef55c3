@Entry
@Component
struct Index {
  @State message: string = 'Hello World'
  @State clickCount: number = 0

  build() {
    Row() {
      Column() {
        // 标题文本
        Text(this.message)
          .fontSize(50)
          .fontWeight(FontWeight.Bold)
          .fontColor('#FF1744')
          .margin({ bottom: 20 })

        // 副标题
        Text('欢迎来到鸿蒙世界！')
          .fontSize(24)
          .fontColor('#333333')
          .margin({ bottom: 30 })

        // 点击计数显示
        Text(`点击次数: ${this.clickCount}`)
          .fontSize(18)
          .fontColor('#666666')
          .margin({ bottom: 20 })

        // 交互按钮
        Button('点击我')
          .type(ButtonType.Capsule)
          .width(200)
          .height(50)
          .fontSize(18)
          .fontColor(Color.White)
          .backgroundColor('#007DFF')
          .onClick(() => {
            this.clickCount++
            if (this.clickCount % 5 === 0) {
              this.message = `Hello World ${this.clickCount}!`
            }
          })
          .margin({ bottom: 30 })

        // 重置按钮
        Button('重置')
          .type(ButtonType.Normal)
          .width(120)
          .height(40)
          .fontSize(16)
          .fontColor('#007DFF')
          .backgroundColor(Color.Transparent)
          .border({
            width: 1,
            color: '#007DFF',
            radius: 20
          })
          .onClick(() => {
            this.clickCount = 0
            this.message = 'Hello World'
          })

        // 底部说明文字
        Text('这是一个鸿蒙HelloWorld应用示例')
          .fontSize(14)
          .fontColor('#999999')
          .margin({ top: 50 })
      }
      .width('100%')
      .height('100%')
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
    }
    .height('100%')
    .backgroundColor('#F5F5F5')
  }
}
