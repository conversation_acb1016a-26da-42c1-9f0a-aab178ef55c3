// 游戏数据模型
interface Position {
  x: number;
  y: number;
}

enum Direction {
  UP = 0,
  RIGHT = 1,
  DOWN = 2,
  LEFT = 3
}

enum GameState {
  READY = 0,
  PLAYING = 1,
  PAUSED = 2,
  GAME_OVER = 3
}

@Entry
@Component
struct Index {
  // 游戏配置
  private readonly GRID_SIZE: number = 20; // 网格大小
  private readonly CANVAS_WIDTH: number = 320; // 画布宽度
  private readonly CANVAS_HEIGHT: number = 320; // 画布高度
  private readonly GAME_SPEED: number = 200; // 游戏速度(毫秒)

  // 游戏状态
  @State gameState: GameState = GameState.READY;
  @State score: number = 0;
  @State highScore: number = 0;

  // 蛇的数据
  @State snake: Position[] = [{ x: 8, y: 8 }];
  @State direction: Direction = Direction.RIGHT;
  @State nextDirection: Direction = Direction.RIGHT;

  // 食物位置
  @State food: Position = { x: 12, y: 8 };

  // Canvas渲染上下文
  private canvasContext: CanvasRenderingContext2D | null = null;
  private gameTimer: number = -1;

  // 组件生命周期
  aboutToAppear() {
    this.loadHighScore();
    this.generateFood();
  }

  aboutToDisappear() {
    this.stopGame();
  }

  // 加载最高分
  loadHighScore() {
    // 这里可以从本地存储加载最高分
    this.highScore = 0;
  }

  // 保存最高分
  saveHighScore() {
    if (this.score > this.highScore) {
      this.highScore = this.score;
      // 这里可以保存到本地存储
    }
  }

  // 开始游戏
  startGame() {
    if (this.gameState === GameState.READY || this.gameState === GameState.GAME_OVER) {
      this.resetGame();
    }
    this.gameState = GameState.PLAYING;
    this.gameTimer = setInterval(() => {
      this.gameLoop();
    }, this.GAME_SPEED);
  }

  // 暂停游戏
  pauseGame() {
    if (this.gameState === GameState.PLAYING) {
      this.gameState = GameState.PAUSED;
      this.stopGame();
    } else if (this.gameState === GameState.PAUSED) {
      this.startGame();
    }
  }

  // 停止游戏
  stopGame() {
    if (this.gameTimer !== -1) {
      clearInterval(this.gameTimer);
      this.gameTimer = -1;
    }
  }

  // 重置游戏
  resetGame() {
    this.snake = [{ x: 8, y: 8 }];
    this.direction = Direction.RIGHT;
    this.nextDirection = Direction.RIGHT;
    this.score = 0;
    this.generateFood();
    this.gameState = GameState.READY;
    this.stopGame();
  }

  // 游戏主循环
  gameLoop() {
    if (this.gameState !== GameState.PLAYING) {
      return;
    }

    this.direction = this.nextDirection;
    this.moveSnake();

    if (this.checkCollision()) {
      this.gameOver();
      return;
    }

    if (this.checkFoodCollision()) {
      this.eatFood();
    }

    this.draw();
  }

  // 移动蛇
  moveSnake() {
    const head = { ...this.snake[0] };

    switch (this.direction) {
      case Direction.UP:
        head.y--;
        break;
      case Direction.RIGHT:
        head.x++;
        break;
      case Direction.DOWN:
        head.y++;
        break;
      case Direction.LEFT:
        head.x--;
        break;
    }

    this.snake.unshift(head);

    // 如果没有吃到食物，移除尾部
    if (!this.checkFoodCollision()) {
      this.snake.pop();
    }
  }

  // 检查碰撞
  checkCollision(): boolean {
    const head = this.snake[0];

    // 检查墙壁碰撞
    if (head.x < 0 || head.x >= this.CANVAS_WIDTH / this.GRID_SIZE ||
        head.y < 0 || head.y >= this.CANVAS_HEIGHT / this.GRID_SIZE) {
      return true;
    }

    // 检查自身碰撞
    for (let i = 1; i < this.snake.length; i++) {
      if (head.x === this.snake[i].x && head.y === this.snake[i].y) {
        return true;
      }
    }

    return false;
  }

  // 检查食物碰撞
  checkFoodCollision(): boolean {
    const head = this.snake[0];
    return head.x === this.food.x && head.y === this.food.y;
  }

  // 吃食物
  eatFood() {
    this.score += 10;
    this.generateFood();
  }

  // 生成食物
  generateFood() {
    const maxX = this.CANVAS_WIDTH / this.GRID_SIZE;
    const maxY = this.CANVAS_HEIGHT / this.GRID_SIZE;

    do {
      this.food = {
        x: Math.floor(Math.random() * maxX),
        y: Math.floor(Math.random() * maxY)
      };
    } while (this.isPositionOnSnake(this.food));
  }

  // 检查位置是否在蛇身上
  isPositionOnSnake(pos: Position): boolean {
    return this.snake.some(segment => segment.x === pos.x && segment.y === pos.y);
  }

  // 游戏结束
  gameOver() {
    this.gameState = GameState.GAME_OVER;
    this.saveHighScore();
    this.stopGame();
  }

  // 改变方向
  changeDirection(newDirection: Direction) {
    // 防止反向移动
    if (Math.abs(this.direction - newDirection) === 2) {
      return;
    }
    this.nextDirection = newDirection;
  }

  // 绘制游戏画面
  draw() {
    if (!this.canvasContext) {
      return;
    }

    const ctx = this.canvasContext;

    // 清空画布
    ctx.fillStyle = '#2C3E50';
    ctx.fillRect(0, 0, this.CANVAS_WIDTH, this.CANVAS_HEIGHT);

    // 绘制网格线
    ctx.strokeStyle = '#34495E';
    ctx.lineWidth = 1;
    for (let i = 0; i <= this.CANVAS_WIDTH; i += this.GRID_SIZE) {
      ctx.beginPath();
      ctx.moveTo(i, 0);
      ctx.lineTo(i, this.CANVAS_HEIGHT);
      ctx.stroke();
    }
    for (let i = 0; i <= this.CANVAS_HEIGHT; i += this.GRID_SIZE) {
      ctx.beginPath();
      ctx.moveTo(0, i);
      ctx.lineTo(this.CANVAS_WIDTH, i);
      ctx.stroke();
    }

    // 绘制蛇
    this.snake.forEach((segment, index) => {
      ctx.fillStyle = index === 0 ? '#E74C3C' : '#27AE60'; // 头部红色，身体绿色
      ctx.fillRect(
        segment.x * this.GRID_SIZE + 1,
        segment.y * this.GRID_SIZE + 1,
        this.GRID_SIZE - 2,
        this.GRID_SIZE - 2
      );
    });

    // 绘制食物
    ctx.fillStyle = '#F39C12';
    ctx.fillRect(
      this.food.x * this.GRID_SIZE + 2,
      this.food.y * this.GRID_SIZE + 2,
      this.GRID_SIZE - 4,
      this.GRID_SIZE - 4
    );
  }

  // Canvas就绪回调
  onCanvasReady(context: CanvasRenderingContext2D) {
    this.canvasContext = context;
    this.draw();
  }

  // 触摸手势处理
  handlePanGesture(event: GestureEvent) {
    if (this.gameState !== GameState.PLAYING) {
      return;
    }

    const deltaX = event.offsetX;
    const deltaY = event.offsetY;

    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      // 水平移动
      if (deltaX > 30) {
        this.changeDirection(Direction.RIGHT);
      } else if (deltaX < -30) {
        this.changeDirection(Direction.LEFT);
      }
    } else {
      // 垂直移动
      if (deltaY > 30) {
        this.changeDirection(Direction.DOWN);
      } else if (deltaY < -30) {
        this.changeDirection(Direction.UP);
      }
    }
  }

  // 获取游戏状态文本
  getGameStateText(): string {
    switch (this.gameState) {
      case GameState.READY:
        return '准备开始';
      case GameState.PLAYING:
        return '游戏中';
      case GameState.PAUSED:
        return '已暂停';
      case GameState.GAME_OVER:
        return '游戏结束';
      default:
        return '';
    }
  }

  // 获取主按钮文本
  getMainButtonText(): string {
    switch (this.gameState) {
      case GameState.READY:
        return '开始游戏';
      case GameState.PLAYING:
        return '暂停';
      case GameState.PAUSED:
        return '继续';
      case GameState.GAME_OVER:
        return '重新开始';
      default:
        return '开始';
    }
  }

  build() {
    Column() {
      // 标题
      Text('贪吃蛇游戏')
        .fontSize(28)
        .fontWeight(FontWeight.Bold)
        .fontColor('#2C3E50')
        .margin({ top: 20, bottom: 10 })

      // 分数显示
      Row() {
        Text(`分数: ${this.score}`)
          .fontSize(18)
          .fontColor('#27AE60')
          .fontWeight(FontWeight.Medium)

        Blank()

        Text(`最高分: ${this.highScore}`)
          .fontSize(18)
          .fontColor('#E74C3C')
          .fontWeight(FontWeight.Medium)
      }
      .width('90%')
      .margin({ bottom: 10 })

      // 游戏状态
      Text(this.getGameStateText())
        .fontSize(16)
        .fontColor('#7F8C8D')
        .margin({ bottom: 20 })

      // 游戏画布
      Canvas(this.canvasContext)
        .width(this.CANVAS_WIDTH)
        .height(this.CANVAS_HEIGHT)
        .backgroundColor('#2C3E50')
        .border({
          width: 2,
          color: '#34495E',
          radius: 8
        })
        .onReady(() => {
          this.onCanvasReady(this.canvasContext!);
        })
        .gesture(
          PanGesture({ fingers: 1, distance: 10 })
            .onActionEnd((event: GestureEvent) => {
              this.handlePanGesture(event);
            })
        )
        .margin({ bottom: 20 })

      // 控制按钮
      Row() {
        Button(this.getMainButtonText())
          .type(ButtonType.Capsule)
          .width(120)
          .height(40)
          .fontSize(16)
          .backgroundColor('#3498DB')
          .onClick(() => {
            if (this.gameState === GameState.READY || this.gameState === GameState.GAME_OVER) {
              this.startGame();
            } else {
              this.pauseGame();
            }
          })

        Button('重置')
          .type(ButtonType.Normal)
          .width(80)
          .height(40)
          .fontSize(16)
          .fontColor('#E74C3C')
          .backgroundColor(Color.Transparent)
          .border({
            width: 1,
            color: '#E74C3C',
            radius: 20
          })
          .onClick(() => {
            this.resetGame();
          })
          .margin({ left: 20 })
      }
      .margin({ bottom: 20 })

      // 方向控制按钮
      Column() {
        Text('方向控制')
          .fontSize(14)
          .fontColor('#7F8C8D')
          .margin({ bottom: 10 })

        Column() {
          // 上
          Button('↑')
            .width(50)
            .height(50)
            .fontSize(24)
            .backgroundColor('#95A5A6')
            .onClick(() => this.changeDirection(Direction.UP))
            .margin({ bottom: 5 })

          Row() {
            // 左
            Button('←')
              .width(50)
              .height(50)
              .fontSize(24)
              .backgroundColor('#95A5A6')
              .onClick(() => this.changeDirection(Direction.LEFT))
              .margin({ right: 10 })

            // 右
            Button('→')
              .width(50)
              .height(50)
              .fontSize(24)
              .backgroundColor('#95A5A6')
              .onClick(() => this.changeDirection(Direction.RIGHT))
              .margin({ left: 10 })
          }
          .margin({ bottom: 5 })

          // 下
          Button('↓')
            .width(50)
            .height(50)
            .fontSize(24)
            .backgroundColor('#95A5A6')
            .onClick(() => this.changeDirection(Direction.DOWN))
        }
      }
      .margin({ bottom: 20 })

      // 游戏说明
      Text('滑动屏幕或点击方向键控制蛇的移动')
        .fontSize(12)
        .fontColor('#BDC3C7')
        .textAlign(TextAlign.Center)
        .margin({ bottom: 20 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#ECF0F1')
    .alignItems(HorizontalAlign.Center)
    .justifyContent(FlexAlign.Start)
    .padding({ top: 20, bottom: 20 })
  }
}
