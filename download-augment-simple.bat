@echo off
:: 简单版本 - 下载Augment VSCode插件
:: 使用方法: download-augment-simple.bat [版本号]
:: 例如: download-augment-simple.bat 0.513.0

:: 设置默认版本
set "VERSION=0.513.0"

:: 如果提供了参数，使用参数作为版本号
if not "%1"=="" set "VERSION=%1"

set "FILENAME=augment-vscode-augment-%VERSION%.vsix"
set "URL=https://marketplace.visualstudio.com/_apis/public/gallery/publishers/augment/vsextensions/vscode-augment/%VERSION%/vspackage"

echo Downloading Augment VSCode Plugin v%VERSION%...
echo.

:: 使用curl下载
curl -L "%URL%" -o "%FILENAME%"

if %errorlevel% equ 0 (
    echo.
    echo Success! Downloaded: %FILENAME%
    echo.
    echo To install: code --install-extension "%FILENAME%"
) else (
    echo.
    echo Download failed! Version %VERSION% may not exist.
    echo Try checking available versions in VSCode.
)

pause
