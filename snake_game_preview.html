<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鸿蒙贪吃蛇游戏预览</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ECF0F1;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .phone-container {
            width: 375px;
            height: 812px;
            background-color: #ECF0F1;
            border: 3px solid #2C3E50;
            border-radius: 30px;
            padding: 20px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
            overflow-y: auto;
        }
        
        .game-title {
            font-size: 28px;
            font-weight: bold;
            color: #2C3E50;
            margin-bottom: 10px;
        }
        
        .score-row {
            display: flex;
            justify-content: space-between;
            width: 90%;
            margin-bottom: 10px;
        }
        
        .score-text {
            font-size: 18px;
            font-weight: 500;
        }
        
        .current-score {
            color: #27AE60;
        }
        
        .high-score {
            color: #E74C3C;
        }
        
        .game-status {
            font-size: 16px;
            color: #7F8C8D;
            margin-bottom: 20px;
        }
        
        .game-canvas {
            width: 320px;
            height: 320px;
            background-color: #2C3E50;
            border: 2px solid #34495E;
            border-radius: 8px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(to right, #34495E 1px, transparent 1px),
                linear-gradient(to bottom, #34495E 1px, transparent 1px);
            background-size: 20px 20px;
        }
        
        .snake-head {
            position: absolute;
            width: 18px;
            height: 18px;
            background-color: #E74C3C;
            top: 161px;
            left: 161px;
        }
        
        .snake-body {
            position: absolute;
            width: 18px;
            height: 18px;
            background-color: #27AE60;
        }
        
        .food {
            position: absolute;
            width: 16px;
            height: 16px;
            background-color: #F39C12;
            top: 162px;
            left: 242px;
        }
        
        .control-buttons {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .main-button {
            width: 120px;
            height: 40px;
            background-color: #3498DB;
            color: white;
            border: none;
            border-radius: 20px;
            font-size: 16px;
            cursor: pointer;
        }
        
        .reset-button {
            width: 80px;
            height: 40px;
            background-color: transparent;
            color: #E74C3C;
            border: 1px solid #E74C3C;
            border-radius: 20px;
            font-size: 16px;
            cursor: pointer;
        }
        
        .direction-controls {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .direction-title {
            font-size: 14px;
            color: #7F8C8D;
            margin-bottom: 10px;
        }
        
        .direction-grid {
            display: grid;
            grid-template-areas: 
                ". up ."
                "left . right"
                ". down .";
            gap: 5px;
        }
        
        .direction-btn {
            width: 50px;
            height: 50px;
            background-color: #95A5A6;
            color: white;
            border: none;
            font-size: 24px;
            cursor: pointer;
        }
        
        .up { grid-area: up; }
        .left { grid-area: left; }
        .right { grid-area: right; }
        .down { grid-area: down; }
        
        .game-instruction {
            font-size: 12px;
            color: #BDC3C7;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .info-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            font-size: 12px;
            max-width: 200px;
        }
    </style>
</head>
<body>
    <div class="info-panel">
        <h4>贪吃蛇游戏特性</h4>
        <p>• Canvas绘制游戏画面</p>
        <p>• 触摸手势控制</p>
        <p>• 实时分数统计</p>
        <p>• 游戏状态管理</p>
        <p>• 碰撞检测系统</p>
    </div>
    
    <div class="phone-container">
        <div class="game-title">贪吃蛇游戏</div>
        
        <div class="score-row">
            <span class="score-text current-score">分数: 50</span>
            <span class="score-text high-score">最高分: 120</span>
        </div>
        
        <div class="game-status">游戏中</div>
        
        <div class="game-canvas">
            <div class="grid"></div>
            <div class="snake-head"></div>
            <div class="snake-body" style="top: 161px; left: 141px;"></div>
            <div class="snake-body" style="top: 161px; left: 121px;"></div>
            <div class="snake-body" style="top: 161px; left: 101px;"></div>
            <div class="food"></div>
        </div>
        
        <div class="control-buttons">
            <button class="main-button">暂停</button>
            <button class="reset-button">重置</button>
        </div>
        
        <div class="direction-controls">
            <div class="direction-title">方向控制</div>
            <div class="direction-grid">
                <button class="direction-btn up">↑</button>
                <button class="direction-btn left">←</button>
                <button class="direction-btn right">→</button>
                <button class="direction-btn down">↓</button>
            </div>
        </div>
        
        <div class="game-instruction">
            滑动屏幕或点击方向键控制蛇的移动
        </div>
    </div>
</body>
</html>
