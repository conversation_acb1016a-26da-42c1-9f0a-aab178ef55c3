# 鸿蒙贪吃蛇游戏

这是一个基于ArkTS和Canvas开发的经典贪吃蛇游戏，展示了鸿蒙应用开发的游戏编程能力。

## 游戏特性

- 🐍 经典贪吃蛇游戏玩法
- 🎮 触摸手势控制 + 方向键控制
- 🎯 实时分数统计和最高分记录
- 🎨 精美的游戏界面和动画效果
- ⏸️ 游戏暂停/继续功能
- 🔄 游戏重置功能

## 项目结构

- `AppScope/`: 应用级配置
- `entry/`: 主模块
  - `src/main/ets/`: ArkTS源代码
    - `pages/Index.ets`: 游戏主页面
  - `src/main/resources/`: 资源文件
- `build-profile.json5`: 构建配置
- `oh-package.json5`: 依赖管理

## 游戏控制

1. **触摸手势**: 在游戏画布上滑动来控制蛇的移动方向
2. **方向键**: 点击屏幕下方的方向控制按钮
3. **游戏按钮**: 开始/暂停/重置游戏

## 运行说明

1. 使用DevEco Studio打开项目
2. 连接鸿蒙设备或启动模拟器
3. 点击运行按钮即可体验贪吃蛇游戏

## 技术栈

- ArkTS (TypeScript扩展)
- ArkUI框架 + Canvas绘图
- Stage模型
- 触摸手势识别
- 定时器游戏循环

## 游戏规则

- 控制蛇移动吃食物
- 每吃一个食物得10分
- 撞墙或撞到自己身体游戏结束
- 挑战更高分数！
